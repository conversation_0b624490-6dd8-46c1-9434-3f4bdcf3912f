package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.ConditionService;
import com.taobao.wireless.orange.service.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "条件管理接口")
@RestController
@RequestMapping("/api/conditions")  // 复数形式
public class ConditionController {

    @Autowired
    private ConditionService conditionService;

    @ApiOperation("查询条件列表")
    @GetMapping
    public PaginationResult<ConditionDTO> query(ConditionQueryDTO conditionQueryDTO,
                                                @RequestParam(defaultValue = "1") Integer page,
                                                @RequestParam(defaultValue = "10") Integer size) {
        return conditionService.query(conditionQueryDTO, new Pagination(page, size));
    }

    @ApiOperation("获取所有条件")
    @GetMapping("/all")
    public Result<List<ConditionDTO>> getAll(ConditionQueryDTO query) {
        return conditionService.getAll(query);
    }

    @ApiOperation("新建条件并发布")
    @PostMapping
    public Result<Long> create(@RequestBody ConditionCreateDTO condition) {
        return null;
    }

    @ApiOperation("查询条件详情")
    @GetMapping("/{conditionId}")
    public Result<ConditionDetailDTO> getByConditionId(@PathVariable("conditionId") String conditionId) {
        return conditionService.getByConditionId(conditionId);
    }

    @ApiOperation("修改条件并发布")
    @PutMapping("/{id}")
    public Result<Boolean> update(@PathVariable("id") Long id, @RequestBody ConditionUpdateDTO condition) {
        return null;
    }

    @ApiOperation("查询设备量")
    @GetMapping("/{id}/devices/count")
    public Result<Long> countDevices(@PathVariable("id") Long id) {
        return null;
    }
}
