package com.taobao.wireless.orange.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.mtop.api.util.Assert;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.ParameterStatus;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterConditionVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.manager.model.ConditionVersionBO;
import com.taobao.wireless.orange.manager.model.ParameterBO;
import com.taobao.wireless.orange.manager.model.ParameterConditionVersionBO;
import com.taobao.wireless.orange.manager.model.ParameterVersionBO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.CONDITION_SEPARATOR;
import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;

@Service
@Validated
public class ParameterManager {

    @Autowired
    private OParameterDAO parameterDAO;
    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;
    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;
    @Autowired
    private OParameterVersionDAO parameterVersionDAO;
    @Autowired
    private ConditionManager conditionManager;

    /**
     * 查询参数列表，支持分页和条件查询
     *
     * @param query 查询条件
     * @param pagination 分页信息
     * @return 参数分页列表结果
     */
    public Page<ParameterBO> query(ParameterBO query, Pagination pagination) {
        Assert.notNull(query, "查询条件不能为空");

        Page<OParameterDO> pageResult = parameterDAO.lambdaQuery()
                .ne(OParameterDO::getStatus, ParameterStatus.DELETED)
                .eq(StringUtils.isNotBlank(query.getNamespaceId()), OParameterDO::getNamespaceId, query.getNamespaceId())
                .eq(query.getStatus() != null, OParameterDO::getStatus, query.getStatus())
                .like(StringUtils.isNotBlank(query.getParameterKey()), OParameterDO::getParameterKey, query.getParameterKey())
                .page(PageUtil.build(pagination));

        Page<ParameterBO> result = BeanUtil.createFromProperties(pageResult, Page.class);

        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            result.setRecords(new ArrayList<>());
            return result;
        }

        // 转换并填充详情
        List<ParameterBO> parameterBOs = BeanUtil.createFromProperties(pageResult.getRecords(), ParameterBO.class);
        fillParameterOnlineDetail(parameterBOs);

        return result.setRecords(parameterBOs);
    }

    /**
     * 为参数列表填充当前线上生效配置详情
     */
    private void fillParameterOnlineDetail(List<ParameterBO> parameterBOs) {
        if (CollectionUtils.isEmpty(parameterBOs)) {
            return;
        }

        List<String> parameterIds = parameterBOs.stream()
                .map(ParameterBO::getParameterId)
                .collect(Collectors.toList());

        // 批量获取线上版本和条件
        var releasedParameterVersionMap = getOnlineParameterVersions(parameterIds);
        var parameterConditionVersionMap = getOnlineParameterConditionVersions(parameterIds);

        // 批量获取发布中版本和发布单
        Map<String, OParameterVersionDO> inPublishParameterVersionMap = getParameterVersionsByStatus(parameterIds, VersionStatus.INIT, false);
        Map<String, OReleaseOrderDO> releaseVersion2Order = getReleaseVersion2Order(inPublishParameterVersionMap);

        // 为每个参数填充详情
        for (ParameterBO parameter : parameterBOs) {
            String parameterId = parameter.getParameterId();

            // 有进行中的发布单，则返回对应发布单信息
            Optional.ofNullable(inPublishParameterVersionMap.get(parameterId))
                    .ifPresent(p -> parameter.setInPublishReleaseOrder(releaseVersion2Order.get(p.getReleaseVersion())));

            OParameterVersionDO parameterVersion = releasedParameterVersionMap.get(parameterId);
            if (parameterVersion != null) {
                parameter.setReleasedParameterVersion(parameterVersion);

                // 填充参数条件（按照优先级排序）
                Optional.ofNullable(parameterConditionVersionMap.get(parameterId))
                        .ifPresent(parameterConditions -> {
                            Map<String, OParameterConditionVersionDO> conditionId2ParameterCondition = parameterConditions.stream().collect(Collectors.toMap(OParameterConditionVersionDO::getConditionId, Function.identity(), (v1, v2) -> v1));

                            List<OParameterConditionVersionDO> versions = getConditionsOrder(parameterVersion)
                                    .stream()
                                    .map(conditionId2ParameterCondition::get)
                                    .collect(Collectors.toList());

                            // 尾部追加默认条件版本记录
                            versions.add(conditionId2ParameterCondition.get(DEFAULT_CONDITION_ID));

                            parameter.setReleasedParameterConditionVersions(versions);
                        });
            }
        }
    }

    /**
     * 获取发布版本和发布单的映射
     *
     * @param inPublishParameterVersionMap
     * @return
     */
    private Map<String, OReleaseOrderDO> getReleaseVersion2Order(Map<String, OParameterVersionDO> inPublishParameterVersionMap) {
        if (MapUtils.isEmpty(inPublishParameterVersionMap)) {
            return new HashMap<>(0);
        }
        List<String> releaseVersions = inPublishParameterVersionMap.values()
                .stream()
                .map(OParameterVersionDO::getReleaseVersion)
                .collect(Collectors.toList());

        return releaseOrderDAO.lambdaQuery()
                .in(OReleaseOrderDO::getReleaseVersion, releaseVersions)
                .list()
                .stream()
                .collect(Collectors.toMap(OReleaseOrderDO::getReleaseVersion, Function.identity(), (v1, v2) -> v1));
    }

    public List<String> getConditionsOrder(OParameterVersionDO parameterVersion) {
        return Optional.ofNullable(parameterVersion.getConditionsOrder())
                .filter(StringUtils::isNotBlank)
                .map(conditionsOrder -> Arrays.asList(conditionsOrder.split(CONDITION_SEPARATOR)))
                .orElse(new ArrayList<>());
    }

    /**
     * 获取线上的参数版本
     */
    public Map<String, OParameterVersionDO> getOnlineParameterVersions(List<String> parameterIds) {
        return getParameterVersionsByStatus(parameterIds, VersionStatus.RELEASED, true);
    }

    /**
     * 获取指定状态参数对应的版本
     */
    public Map<String, OParameterVersionDO> getParameterVersionsByStatus(List<String> parameterIds, VersionStatus status, boolean isExist) {
        if (CollectionUtils.isEmpty(parameterIds)) {
            return Collections.emptyMap();
        }

        return parameterVersionDAO.lambdaQuery()
                .in(OParameterVersionDO::getParameterId, parameterIds)
                .eq(OParameterVersionDO::getStatus, status)
                .ne(isExist, OParameterVersionDO::getChangeType, ChangeType.DELETE)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        OParameterVersionDO::getParameterId,
                        Function.identity(),
                        (v1, v2) -> v1
                ));
    }

    /**
     * 获取线上的参数条件
     */
    public Map<String, List<OParameterConditionVersionDO>> getOnlineParameterConditionVersions(List<String> parameterIds) {
        return getParameterConditionVersionsByStatus(parameterIds, VersionStatus.RELEASED, true);
    }

    public Map<String, List<OParameterConditionVersionDO>> getParameterConditionVersionsByStatus(List<String> parameterIds, VersionStatus versionStatus, boolean isExist) {
        if (CollectionUtils.isEmpty(parameterIds)) {
            return Collections.emptyMap();
        }

        return parameterConditionVersionDAO.lambdaQuery()
                .in(OParameterConditionVersionDO::getParameterId, parameterIds)
                .eq(OParameterConditionVersionDO::getStatus, versionStatus)
                .ne(isExist, OParameterConditionVersionDO::getChangeType, ChangeType.DELETE)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId,
                        Collectors.toList())
                );
    }

    /**
     * 批量创建参数变更
     *
     * @param namespace         命名空间
     * @param releaseVersion    发布版本
     * @param parameterVersions 参数版本列表
     */
    public void createParameterVersions(ONamespaceDO namespace, String releaseVersion, List<ParameterVersionBO> parameterVersions) {
        if (CollectionUtils.isEmpty(parameterVersions)) {
            return;
        }

        // 获取参数IDs并查询现有的在线版本
        List<String> parameterIds = parameterVersions.stream()
                .map(ParameterVersionBO::getParameterId)
                .collect(Collectors.toList());
        var onlineParamId2Version = getOnlineParameterVersions(parameterIds);

        // 处理参数版本
        String appKey = namespace.getAppKey();
        String namespaceId = namespace.getNamespaceId();
        List<OParameterVersionDO> newParameterVersions = processParameterVersions(appKey, namespaceId, releaseVersion, parameterVersions, onlineParamId2Version);
        // 处理参数实体
        List<OParameterDO> newParameters = processParameterEntities(appKey, namespaceId, parameterVersions);

        // 保存参数实体
        parameterDAO.saveOrUpdateBatchByParameterId(newParameters);
        // 保存参数版本
        parameterVersionDAO.saveBatch(newParameterVersions);
    }

    /**
     * 处理参数实体
     */
    private List<OParameterDO> processParameterEntities(String appKey, String namespaceId,
                                                        List<ParameterVersionBO> parameterVersions) {
        return parameterVersions.stream()
                .map(parameterVersionBO -> {
                    ParameterBO parameterBO = parameterVersionBO.getParameterBO();

                    // 处理新增参数的情况
                    if (StringUtils.isBlank(parameterBO.getParameterId())) {
                        String parameterId = SerializeUtil.UUID();
                        parameterVersionBO.setParameterId(parameterId);
                        parameterBO.setAppKey(appKey);
                        parameterBO.setNamespaceId(namespaceId);
                        parameterBO.setParameterId(parameterId);
                    }

                    parameterBO.setStatus(ParameterStatus.VALID);
                    return parameterBO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理参数版本
     */
    private List<OParameterVersionDO> processParameterVersions(String appKey,
                                                               String namespaceId,
                                                               String releaseVersion,
                                                               List<ParameterVersionBO> parameterVersions,
                                                               Map<String, OParameterVersionDO> onlineParamId2Version) {
        return parameterVersions.stream().map(parameterVersion -> {
                    ParameterBO parameterBO = parameterVersion.getParameterBO();

                    List<String> conditionIds = conditionManager.getOrderedConditionIdsByName(namespaceId, parameterVersion.getConditionNamesOrder());
                    parameterVersion.setConditionsOrder(StringUtils.join(conditionIds, CONDITION_SEPARATOR));
                    parameterVersion.setStatus(VersionStatus.INIT);
                    parameterVersion.setAppKey(appKey);
                    parameterVersion.setNamespaceId(namespaceId);
                    parameterVersion.setReleaseVersion(releaseVersion);
                    parameterVersion.setParameterKey(parameterBO.getParameterKey());

                    // 验证版本一致性
                    OParameterVersionDO onlineParameterVersion = onlineParamId2Version.get(parameterBO.getParameterId());
                    if (onlineParameterVersion != null &&
                            !onlineParameterVersion.getReleaseVersion().equals(parameterVersion.getPreviousReleaseVersion())) {
                        throw CommonException.getDynamicException(
                                ExceptionEnum.PARAMETER_PREVIOUS_RELEASE_VERSION_NOT_MATCH,
                                parameterBO.getParameterKey()
                        );
                    }
                    if (onlineParameterVersion != null) {
                        // 如果非新增参数，则不使用传入的参数值类型而使用数据库中的
                        parameterVersion.setValueType(onlineParameterVersion.getValueType());
                    }

                    return parameterVersion;
                })
                .collect(Collectors.toList());
    }

    /**
     * 新增参数条件版本记录
     *
     * @param namespace           命名空间
     * @param releaseVersion      发布版本
     * @param parameterVersionBOs 参数版本列表
     * @param conditionVersionBOs 条件版本列表
     */
    public void createParameterConditionVersions(ONamespaceDO namespace,
                                                 String releaseVersion,
                                                 List<ParameterVersionBO> parameterVersionBOs,
                                                 List<ConditionVersionBO> conditionVersionBOs) {
        if (CollectionUtils.isEmpty(parameterVersionBOs)) {
            return;
        }

        // 构建条件名称到ID的映射
        Map<String, String> conditionName2Id = conditionVersionBOs.stream()
                .collect(Collectors.toMap(
                        v -> v.getCondition().getName(),
                        ConditionVersionBO::getConditionId
                ));

        List<String> parameterIds = parameterVersionBOs.stream()
                .map(ParameterVersionBO::getParameterId)
                .collect(Collectors.toList());

        // 获取线上的参数条件版本
        var onlineParameterConditionVersions = getOnlineParameterConditionVersions(parameterIds);

        // 构建新的参数条件版本
        List<OParameterConditionVersionDO> newParameterConditionVersions =
                buildParameterConditionVersions(parameterVersionBOs, conditionName2Id,
                        onlineParameterConditionVersions, namespace.getAppKey(),
                        namespace.getNamespaceId(), releaseVersion);

        // 保存参数条件版本
        if (!CollectionUtils.isEmpty(newParameterConditionVersions)) {
            parameterConditionVersionDAO.saveBatch(newParameterConditionVersions);
        }
    }

    /**
     * 构建参数条件版本列表
     */
    private List<OParameterConditionVersionDO> buildParameterConditionVersions(
            List<ParameterVersionBO> parameterVersionBOs,
            Map<String, String> conditionName2Id,
            Map<String, List<OParameterConditionVersionDO>> onlineParameterConditionVersions,
            String appKey,
            String namespaceId,
            String releaseVersion) {

        List<OParameterConditionVersionDO> newParameterConditionVersions = new ArrayList<>();

        for (ParameterVersionBO parameterVersionBO : parameterVersionBOs) {
            List<ParameterConditionVersionBO> parameterConditionVersionBOS = parameterVersionBO.getParameterConditionVersionBOS();
            if (CollectionUtils.isEmpty(parameterConditionVersionBOS)) {
                continue;
            }
            ParameterBO parameterBO = parameterVersionBO.getParameterBO();

            List<OParameterConditionVersionDO> onlineParameterConditions = onlineParameterConditionVersions.get(parameterVersionBO.getParameterId());

            Map<String, String> conditionId2ReleaseVersion = Optional.ofNullable(onlineParameterConditions)
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.toMap(OParameterConditionVersionDO::getConditionId, OParameterConditionVersionDO::getReleaseVersion));

            for (ParameterConditionVersionBO parameterConditionVersionBO : parameterConditionVersionBOS) {
                // 说明引用了本次新增的条件
                if (parameterConditionVersionBO.getConditionId() == null) {
                    String conditionName = parameterConditionVersionBO.getConditionName();
                    parameterConditionVersionBO.setConditionId(conditionName2Id.get(conditionName));
                }
                parameterConditionVersionBO.setAppKey(appKey);
                parameterConditionVersionBO.setNamespaceId(namespaceId);
                parameterConditionVersionBO.setReleaseVersion(releaseVersion);
                parameterConditionVersionBO.setParameterId(parameterBO.getParameterId());
                parameterConditionVersionBO.setStatus(VersionStatus.INIT);

                String releasedReleaseVersion = conditionId2ReleaseVersion.get(parameterConditionVersionBO.getConditionId());

                if (StringUtils.isNotBlank(releasedReleaseVersion) &&
                        !releasedReleaseVersion.equals(parameterConditionVersionBO.getPreviousReleaseVersion())) {
                    throw CommonException.getDynamicException(ExceptionEnum.PARAMETER_CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH,
                            parameterBO.getParameterKey(),
                            parameterConditionVersionBO.getConditionName());
                }
            }

            newParameterConditionVersions.addAll(parameterConditionVersionBOS);
        }

        return newParameterConditionVersions;
    }
}
