package com.taobao.wireless.orange.manager.example;

import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.manager.model.OONamespaceBO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.groups.Default;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 命名空间校验示例类
 * 展示如何使用注解进行参数校验，替代手动编写校验代码
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NamespaceValidationExample {

    /**
     * 示例1：基础参数校验
     * 使用 @NotNull 校验参数不为空
     */
    @AttributeValidate
    public void basicValidation(@NotNull(message = "命名空间对象不能为空") OONamespaceBO namespace) {
        // 不需要手动写 Assert.notNull(namespace, "命名空间对象不能为空");
        // 框架会自动校验
        System.out.println("处理命名空间: " + namespace.getName());
    }

    /**
     * 示例2：对象属性校验
     * 使用 @Valid 校验对象内部属性
     */
    @AttributeValidate
    public void objectValidation(@Valid @NotNull(message = "命名空间对象不能为空") OONamespaceBO namespace) {
        // @Valid 会触发对 namespace 对象内部字段的校验
        // 比如 @NotBlank 标注的字段会被自动校验
        System.out.println("处理命名空间: " + namespace.getName());
    }

    /**
     * 示例3：多参数校验
     * 同时校验多个参数
     */
    @AttributeValidate
    public void multipleParametersValidation(
            @Valid @NotNull(message = "命名空间对象不能为空") OONamespaceBO namespace,
            @NotNull(message = "分页信息不能为空") Pagination pagination) {
        // 两个参数都会被自动校验
        System.out.println("查询命名空间，页码: " + pagination.getPage());
    }

    /**
     * 示例4：校验分组使用
     * 根据不同场景使用不同的校验规则
     */
    @AttributeValidate
    public void createNamespace(
            @Validated(OONamespaceBO.CreateValidation.class) 
            @NotNull(message = "命名空间对象不能为空") OONamespaceBO namespace) {
        // 只会校验标注了 CreateValidation.class 分组的字段
        // 比如 appKey、bizType、bizId、name 等创建时必填的字段
        System.out.println("创建命名空间: " + namespace.getName());
    }

    /**
     * 示例5：查询场景的校验
     * 查询时只需要校验部分字段
     */
    @AttributeValidate
    public void queryNamespace(
            @Validated(OONamespaceBO.QueryValidation.class) 
            @NotNull(message = "查询条件不能为空") OONamespaceBO query,
            @NotNull(message = "分页信息不能为空") Pagination pagination) {
        // 只会校验标注了 QueryValidation.class 分组的字段
        // 比如 appKey 等查询时必填的字段
        System.out.println("查询命名空间，应用: " + query.getAppKey());
    }

    /**
     * 示例6：更新场景的校验
     * 更新时需要校验不同的字段
     */
    @AttributeValidate
    public void updateNamespace(
            @Validated(OONamespaceBO.UpdateValidation.class) 
            @NotNull(message = "命名空间对象不能为空") OONamespaceBO namespace) {
        // 只会校验标注了 UpdateValidation.class 分组的字段
        // 比如 status 等更新时需要校验的字段
        System.out.println("更新命名空间: " + namespace.getNamespaceId());
    }

    /**
     * 示例7：默认校验 + 特定分组校验
     * 同时使用默认校验和特定分组校验
     */
    @AttributeValidate
    public void mixedValidation(
            @Validated({Default.class, OONamespaceBO.CreateValidation.class}) 
            @NotNull(message = "命名空间对象不能为空") OONamespaceBO namespace) {
        // 会校验默认分组和 CreateValidation 分组的所有字段
        System.out.println("混合校验命名空间: " + namespace.getName());
    }
}
