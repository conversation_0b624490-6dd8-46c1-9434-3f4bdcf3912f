package com.taobao.wireless.orange.manager.model;

import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterConditionVersionDO;
import lombok.Data;

import java.util.List;

@Data
public class ConditionVersionBO extends OConditionVersionDO {
    /**
     * 关联的参数实体
     */
    private OConditionDO condition;
    /**
     * 关联的参数版本
     */
    private List<OParameterConditionVersionDO> parameterConditionVersions;
}
