package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.model.User;
import com.taobao.wireless.orange.external.user.AmdpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class UserService {

    @Autowired
    private AmdpService amdpService;

    /**
     * 根据工号批量查询用户
     */
    public Result<Map<String, User>> queryUserByWorkNoList(List<String> workNoList) {
        // todo: 需要支持批量
        return amdpService.queryUserByWorkNoList(workNoList);
    }

    /**
     * 根据关键字模糊搜索用户
     *
     * @param keyword
     * @return
     */
    public Result<List<User>> fuzzySearchUserByKeyword(String keyword) {
        var users = amdpService.fuzzySearchUserByKeyword(keyword);
        return Result.success(users);
    }
}
